# YangSpider酒店管理系统 Dockerfile
# 基于OpenJDK 17的多阶段构建

# 构建阶段
FROM maven:3.9.4-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom.xml和源代码
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:17-jdk-slim

# 设置维护者信息
LABEL maintainer="YangYang <<EMAIL>>"
LABEL description="YangSpider酒店管理系统"
LABEL version="1.0.0"

# 创建应用用户
RUN groupadd -r yangspider && useradd -r -g yangspider yangspider

# 设置工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs && chown -R yangspider:yangspider /app

# 从构建阶段复制jar文件
COPY --from=builder /app/target/yangspider-1.0.0.jar app.jar

# 设置文件权限
RUN chown yangspider:yangspider app.jar

# 切换到应用用户
USER yangspider

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/test/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

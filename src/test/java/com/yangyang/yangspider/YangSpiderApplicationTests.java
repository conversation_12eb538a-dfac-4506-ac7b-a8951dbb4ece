package com.yangyang.yangspider;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * YangSpider应用程序测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@SpringBootTest
@ActiveProfiles("test")
class YangSpiderApplicationTests {

    /**
     * 测试应用程序上下文加载
     */
    @Test
    void contextLoads() {
        // 这个测试确保应用程序上下文能够正确加载
        // 如果Spring Boot应用程序配置有问题，这个测试会失败
    }

    /**
     * 测试应用程序启动
     */
    @Test
    void applicationStarts() {
        // 测试应用程序能够正常启动
        // 这是一个基本的冒烟测试
    }
}

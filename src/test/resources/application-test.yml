# 测试环境配置
server:
  port: 0 # 随机端口

spring:
  datasource:
    # 使用H2内存数据库进行测试
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# 日志配置
logging:
  level:
    com.yangyang.yangspider: DEBUG
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置（测试环境）
yangspider:
  jwt:
    secret: test-jwt-secret-key-for-yangspider-hotel-management-system
    expiration: 3600000 # 1小时
    header: Authorization
    prefix: "Bearer "

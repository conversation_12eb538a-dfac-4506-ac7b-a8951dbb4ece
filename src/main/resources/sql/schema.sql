-- Yang<PERSON><PERSON><PERSON>酒店管理系统数据库表结构
-- 创建时间：2024-08-08
-- 作者：YangYang

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS yangyangchuyou 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE yangyangchuyou;

-- 酒店信息表
DROP TABLE IF EXISTS hotel_info;
CREATE TABLE hotel_info (
    hotel_id BIGINT AUTO_INCREMENT COMMENT '酒店ID（主键，自增）',
    hotel_name VARCHAR(200) NOT NULL COMMENT '酒店名称',
    hotel_longitude DECIMAL(10, 6) NOT NULL COMMENT '酒店经度',
    hotel_latitude DECIMAL(10, 6) NOT NULL COMMENT '酒店纬度',
    hotel_address VARCHAR(500) NOT NULL COMMENT '酒店地址',
    hotel_date DATE NOT NULL COMMENT '酒店日期（入住日期或营业日期）',
    hotel_price DECIMAL(10, 2) NOT NULL COMMENT '酒店价格',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    PRIMARY KEY (hotel_id),
    INDEX idx_hotel_name (hotel_name),
    INDEX idx_hotel_date (hotel_date),
    INDEX idx_hotel_price (hotel_price),
    INDEX idx_location (hotel_longitude, hotel_latitude),
    INDEX idx_create_time (create_time),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='酒店信息表';

-- 用户表（用于Spring Security认证）
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
    user_id BIGINT AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密后）',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    status TINYINT DEFAULT 1 COMMENT '状态（0：禁用，1：启用）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    PRIMARY KEY (user_id),
    UNIQUE INDEX uk_username (username),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 角色表
DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role (
    role_id BIGINT AUTO_INCREMENT COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态（0：禁用，1：启用）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    PRIMARY KEY (role_id),
    UNIQUE INDEX uk_role_code (role_code),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统角色表';

-- 用户角色关联表
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE INDEX uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 定时任务表
DROP TABLE IF EXISTS sys_job;
CREATE TABLE sys_job (
    job_id BIGINT AUTO_INCREMENT COMMENT '任务ID',
    job_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    job_group VARCHAR(50) NOT NULL COMMENT '任务组名',
    job_class VARCHAR(200) NOT NULL COMMENT '任务执行类',
    cron_expression VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    description VARCHAR(500) COMMENT '任务描述',
    status TINYINT DEFAULT 1 COMMENT '状态（0：暂停，1：正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    PRIMARY KEY (job_id),
    UNIQUE INDEX uk_job_name_group (job_name, job_group),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务表';

-- 任务执行日志表
DROP TABLE IF EXISTS sys_job_log;
CREATE TABLE sys_job_log (
    log_id BIGINT AUTO_INCREMENT COMMENT '日志ID',
    job_id BIGINT NOT NULL COMMENT '任务ID',
    job_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    job_group VARCHAR(50) NOT NULL COMMENT '任务组名',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration BIGINT COMMENT '执行时长（毫秒）',
    status TINYINT NOT NULL COMMENT '执行状态（0：失败，1：成功）',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (log_id),
    INDEX idx_job_id (job_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行日志表';

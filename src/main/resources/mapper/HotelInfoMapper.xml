<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yangyang.yangspider.mapper.HotelInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yangyang.yangspider.entity.HotelInfo">
        <id column="hotel_id" property="hotelId" jdbcType="BIGINT"/>
        <result column="hotel_name" property="hotelName" jdbcType="VARCHAR"/>
        <result column="hotel_longitude" property="hotelLongitude" jdbcType="DECIMAL"/>
        <result column="hotel_latitude" property="hotelLatitude" jdbcType="DECIMAL"/>
        <result column="hotel_address" property="hotelAddress" jdbcType="VARCHAR"/>
        <result column="hotel_date" property="hotelDate" jdbcType="DATE"/>
        <result column="hotel_price" property="hotelPrice" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        hotel_id, hotel_name, hotel_longitude, hotel_latitude, hotel_address, 
        hotel_date, hotel_price, create_time, update_time, deleted
    </sql>

    <!-- 分页查询酒店信息（支持多条件查询） -->
    <select id="selectHotelInfoPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        <if test="hotelName != null and hotelName != ''">
            AND hotel_name LIKE CONCAT('%', #{hotelName}, '%')
        </if>
        <if test="minPrice != null">
            AND hotel_price >= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND hotel_price &lt;= #{maxPrice}
        </if>
        <if test="startDate != null">
            AND hotel_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND hotel_date &lt;= #{endDate}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据酒店名称模糊查询 -->
    <select id="findByHotelNameLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        AND hotel_name LIKE CONCAT('%', #{hotelName}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 根据价格区间查询 -->
    <select id="findByPriceRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        AND hotel_price BETWEEN #{minPrice} AND #{maxPrice}
        ORDER BY hotel_price ASC
    </select>

    <!-- 根据日期范围查询 -->
    <select id="findByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        AND hotel_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY hotel_date DESC
    </select>

    <!-- 根据地理位置范围查询 -->
    <select id="findByLocationRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        AND hotel_longitude BETWEEN #{minLongitude} AND #{maxLongitude}
        AND hotel_latitude BETWEEN #{minLatitude} AND #{maxLatitude}
        ORDER BY create_time DESC
    </select>

    <!-- 统计酒店总数 -->
    <select id="countHotels" resultType="java.lang.Long">
        SELECT COUNT(*) FROM hotel_info WHERE deleted = 0
    </select>

    <!-- 获取价格统计信息 -->
    <select id="getPriceStatistics" resultType="java.util.Map">
        SELECT 
            MIN(hotel_price) as minPrice, 
            MAX(hotel_price) as maxPrice, 
            AVG(hotel_price) as avgPrice 
        FROM hotel_info 
        WHERE deleted = 0
    </select>

    <!-- 批量插入酒店信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO hotel_info (hotel_name, hotel_longitude, hotel_latitude, hotel_address, hotel_date, hotel_price)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.hotelName}, #{item.hotelLongitude}, #{item.hotelLatitude}, 
             #{item.hotelAddress}, #{item.hotelDate}, #{item.hotelPrice})
        </foreach>
    </insert>

    <!-- 根据条件统计数量 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM hotel_info
        WHERE deleted = 0
        <if test="hotelName != null and hotelName != ''">
            AND hotel_name LIKE CONCAT('%', #{hotelName}, '%')
        </if>
        <if test="minPrice != null">
            AND hotel_price >= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND hotel_price &lt;= #{maxPrice}
        </if>
        <if test="startDate != null">
            AND hotel_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND hotel_date &lt;= #{endDate}
        </if>
    </select>

    <!-- 根据地址关键词查询 -->
    <select id="findByAddressKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hotel_info
        WHERE deleted = 0
        AND hotel_address LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time DESC
    </select>

</mapper>

package com.yangyang.yangspider.config;

import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * Quartz配置类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Configuration
public class QuartzConfig {

    @Autowired
    private DataSource dataSource;

    /**
     * 调度器工厂Bean
     */
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        
        // 设置数据源
        factory.setDataSource(dataSource);
        
        // 设置Quartz属性
        factory.setQuartzProperties(quartzProperties());
        
        // 设置调度器名称
        factory.setSchedulerName("YangSpiderScheduler");
        
        // 应用启动完成后，等待2秒后开始执行调度器
        factory.setStartupDelay(2);
        
        // 可选，QuartzScheduler启动时更新己存在的Job
        factory.setOverwriteExistingJobs(true);
        
        // 设置自动启动
        factory.setAutoStartup(true);
        
        return factory;
    }

    /**
     * 调度器
     */
    @Bean
    public Scheduler scheduler() throws Exception {
        return schedulerFactoryBean().getScheduler();
    }

    /**
     * Quartz属性配置
     */
    private Properties quartzProperties() {
        Properties properties = new Properties();
        
        // 调度器属性
        properties.setProperty("org.quartz.scheduler.instanceName", "YangSpiderScheduler");
        properties.setProperty("org.quartz.scheduler.instanceId", "AUTO");
        
        // 线程池属性
        properties.setProperty("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        properties.setProperty("org.quartz.threadPool.threadCount", "10");
        properties.setProperty("org.quartz.threadPool.threadPriority", "5");
        properties.setProperty("org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread", "true");
        
        // JobStore属性
        properties.setProperty("org.quartz.jobStore.class", "org.quartz.impl.jdbcjobstore.JobStoreTX");
        properties.setProperty("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.StdJDBCDelegate");
        properties.setProperty("org.quartz.jobStore.tablePrefix", "QRTZ_");
        properties.setProperty("org.quartz.jobStore.isClustered", "false");
        properties.setProperty("org.quartz.jobStore.clusterCheckinInterval", "10000");
        properties.setProperty("org.quartz.jobStore.useProperties", "false");
        
        // 插件配置
        properties.setProperty("org.quartz.plugin.triggHistory.class", "org.quartz.plugins.history.LoggingTriggerHistoryPlugin");
        properties.setProperty("org.quartz.plugin.jobHistory.class", "org.quartz.plugins.history.LoggingJobHistoryPlugin");
        
        return properties;
    }
}

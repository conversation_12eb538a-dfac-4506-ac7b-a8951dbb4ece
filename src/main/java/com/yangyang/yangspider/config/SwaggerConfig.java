package com.yangyang.yangspider.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Configuration
public class SwaggerConfig {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api}")
    private String contextPath;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                    new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("本地开发环境"),
                    new Server()
                        .url("https://api.yangyang.com" + contextPath)
                        .description("生产环境")
                ));
    }

    private Info apiInfo() {
        return new Info()
                .title("YangSpider酒店管理系统API")
                .description("基于Spring Boot的酒店信息管理系统，提供完整的酒店信息CRUD操作、用户认证、定时任务管理等功能。")
                .version("1.0.0")
                .contact(new Contact()
                        .name("YangYang")
                        .email("<EMAIL>")
                        .url("https://github.com/yangyang"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"))
                .termsOfService("https://www.yangyang.com/terms");
    }
}

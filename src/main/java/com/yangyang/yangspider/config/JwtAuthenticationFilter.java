package com.yangyang.yangspider.config;

import com.yangyang.yangspider.common.JwtUtils;
import com.yangyang.yangspider.service.UserDetailsServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        try {
            // 从请求头中获取JWT Token
            String token = getTokenFromRequest(request);
            
            if (StringUtils.hasText(token) && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 从Token中获取用户名
                String username = jwtUtils.getUsernameFromToken(token);
                
                if (StringUtils.hasText(username)) {
                    // 加载用户详情
                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                    
                    // 验证Token
                    if (jwtUtils.validateToken(token, username)) {
                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authentication = 
                            new UsernamePasswordAuthenticationToken(
                                userDetails, 
                                null, 
                                userDetails.getAuthorities()
                            );
                        
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        // 设置认证信息到安全上下文
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        logger.debug("用户 {} 认证成功", username);
                    } else {
                        logger.warn("Token验证失败，用户：{}", username);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("JWT认证过滤器处理异常：", e);
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取Token
     * 
     * @param request HTTP请求
     * @return JWT Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        return jwtUtils.getTokenFromHeader(authHeader);
    }
}

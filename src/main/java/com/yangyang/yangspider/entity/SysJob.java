package com.yangyang.yangspider.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 定时任务实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@TableName("sys_job")
@Schema(description = "定时任务")
public class SysJob {

    /**
     * 任务ID
     */
    @TableId(value = "job_id", type = IdType.AUTO)
    @Schema(description = "任务ID", example = "1")
    private Long jobId;

    /**
     * 任务名称
     */
    @TableField("job_name")
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 100, message = "任务名称长度不能超过100个字符")
    @Schema(description = "任务名称", example = "酒店数据同步任务")
    private String jobName;

    /**
     * 任务组名
     */
    @TableField("job_group")
    @NotBlank(message = "任务组名不能为空")
    @Size(max = 50, message = "任务组名长度不能超过50个字符")
    @Schema(description = "任务组名", example = "HOTEL_GROUP")
    private String jobGroup;

    /**
     * 任务执行类
     */
    @TableField("job_class")
    @NotBlank(message = "任务执行类不能为空")
    @Size(max = 200, message = "任务执行类长度不能超过200个字符")
    @Schema(description = "任务执行类", example = "com.yangyang.yangspider.job.HotelDataSyncJob")
    private String jobClass;

    /**
     * Cron表达式
     */
    @TableField("cron_expression")
    @NotBlank(message = "Cron表达式不能为空")
    @Size(max = 100, message = "Cron表达式长度不能超过100个字符")
    @Schema(description = "Cron表达式", example = "0 0 2 * * ?")
    private String cronExpression;

    /**
     * 任务描述
     */
    @TableField("description")
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    @Schema(description = "任务描述", example = "每天凌晨2点同步酒店数据")
    private String description;

    /**
     * 状态（0：暂停，1：正常）
     */
    @TableField("status")
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志（0：未删除，1：已删除）
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "删除标志", hidden = true)
    private Integer deleted;

    // 构造函数
    public SysJob() {}

    public SysJob(String jobName, String jobGroup, String jobClass, String cronExpression) {
        this.jobName = jobName;
        this.jobGroup = jobGroup;
        this.jobClass = jobClass;
        this.cronExpression = cronExpression;
        this.status = 1; // 默认正常状态
    }

    // Getter和Setter方法
    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getJobClass() {
        return jobClass;
    }

    public void setJobClass(String jobClass) {
        this.jobClass = jobClass;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "SysJob{" +
                "jobId=" + jobId +
                ", jobName='" + jobName + '\'' +
                ", jobGroup='" + jobGroup + '\'' +
                ", jobClass='" + jobClass + '\'' +
                ", cronExpression='" + cronExpression + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                '}';
    }
}

package com.yangyang.yangspider.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 酒店信息实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@TableName("hotel_info")
@Schema(description = "酒店信息")
public class HotelInfo {

    /**
     * 酒店ID（主键，自增）
     */
    @TableId(value = "hotel_id", type = IdType.AUTO)
    @Schema(description = "酒店ID", example = "1")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @TableField("hotel_name")
    @NotBlank(message = "酒店名称不能为空")
    @Size(max = 200, message = "酒店名称长度不能超过200个字符")
    @Schema(description = "酒店名称", example = "北京王府井希尔顿酒店")
    private String hotelName;

    /**
     * 酒店经度
     */
    @TableField("hotel_longitude")
    @NotNull(message = "酒店经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度值必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度值必须在-180到180之间")
    @Schema(description = "酒店经度", example = "116.407526")
    private BigDecimal hotelLongitude;

    /**
     * 酒店纬度
     */
    @TableField("hotel_latitude")
    @NotNull(message = "酒店纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度值必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度值必须在-90到90之间")
    @Schema(description = "酒店纬度", example = "39.904030")
    private BigDecimal hotelLatitude;

    /**
     * 酒店地址
     */
    @TableField("hotel_address")
    @NotBlank(message = "酒店地址不能为空")
    @Size(max = 500, message = "酒店地址长度不能超过500个字符")
    @Schema(description = "酒店地址", example = "北京市东城区王府井大街1号")
    private String hotelAddress;

    /**
     * 酒店日期（入住日期或营业日期）
     */
    @TableField("hotel_date")
    @NotNull(message = "酒店日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "酒店日期", example = "2024-08-08")
    private LocalDate hotelDate;

    /**
     * 酒店价格
     */
    @TableField("hotel_price")
    @NotNull(message = "酒店价格不能为空")
    @DecimalMin(value = "0.0", message = "酒店价格不能为负数")
    @Schema(description = "酒店价格", example = "588.00")
    private BigDecimal hotelPrice;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志（0：未删除，1：已删除）
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "删除标志", hidden = true)
    private Integer deleted;

    // 构造函数
    public HotelInfo() {}

    public HotelInfo(String hotelName, BigDecimal hotelLongitude, BigDecimal hotelLatitude, 
                     String hotelAddress, LocalDate hotelDate, BigDecimal hotelPrice) {
        this.hotelName = hotelName;
        this.hotelLongitude = hotelLongitude;
        this.hotelLatitude = hotelLatitude;
        this.hotelAddress = hotelAddress;
        this.hotelDate = hotelDate;
        this.hotelPrice = hotelPrice;
    }

    // Getter和Setter方法
    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public BigDecimal getHotelLongitude() {
        return hotelLongitude;
    }

    public void setHotelLongitude(BigDecimal hotelLongitude) {
        this.hotelLongitude = hotelLongitude;
    }

    public BigDecimal getHotelLatitude() {
        return hotelLatitude;
    }

    public void setHotelLatitude(BigDecimal hotelLatitude) {
        this.hotelLatitude = hotelLatitude;
    }

    public String getHotelAddress() {
        return hotelAddress;
    }

    public void setHotelAddress(String hotelAddress) {
        this.hotelAddress = hotelAddress;
    }

    public LocalDate getHotelDate() {
        return hotelDate;
    }

    public void setHotelDate(LocalDate hotelDate) {
        this.hotelDate = hotelDate;
    }

    public BigDecimal getHotelPrice() {
        return hotelPrice;
    }

    public void setHotelPrice(BigDecimal hotelPrice) {
        this.hotelPrice = hotelPrice;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "HotelInfo{" +
                "hotelId=" + hotelId +
                ", hotelName='" + hotelName + '\'' +
                ", hotelLongitude=" + hotelLongitude +
                ", hotelLatitude=" + hotelLatitude +
                ", hotelAddress='" + hotelAddress + '\'' +
                ", hotelDate=" + hotelDate +
                ", hotelPrice=" + hotelPrice +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                '}';
    }
}

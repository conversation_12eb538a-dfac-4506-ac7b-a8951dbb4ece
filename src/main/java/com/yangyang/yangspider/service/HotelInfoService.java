package com.yangyang.yangspider.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yangyang.yangspider.entity.HotelInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 酒店信息服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
public interface HotelInfoService extends IService<HotelInfo> {

    /**
     * 分页查询酒店信息
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param hotelName 酒店名称（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 分页结果
     */
    IPage<HotelInfo> pageQuery(Long current, Long size, String hotelName, 
                              BigDecimal minPrice, BigDecimal maxPrice,
                              LocalDate startDate, LocalDate endDate);

    /**
     * 根据酒店名称模糊查询
     * 
     * @param hotelName 酒店名称
     * @return 酒店信息列表
     */
    List<HotelInfo> findByHotelNameLike(String hotelName);

    /**
     * 根据价格区间查询
     * 
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 酒店信息列表
     */
    List<HotelInfo> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 根据日期范围查询
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 酒店信息列表
     */
    List<HotelInfo> findByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 根据地理位置范围查询
     * 
     * @param minLongitude 最小经度
     * @param maxLongitude 最大经度
     * @param minLatitude 最小纬度
     * @param maxLatitude 最大纬度
     * @return 酒店信息列表
     */
    List<HotelInfo> findByLocationRange(BigDecimal minLongitude, BigDecimal maxLongitude,
                                       BigDecimal minLatitude, BigDecimal maxLatitude);

    /**
     * 创建酒店信息
     * 
     * @param hotelInfo 酒店信息
     * @return 是否创建成功
     */
    boolean createHotel(HotelInfo hotelInfo);

    /**
     * 更新酒店信息
     * 
     * @param hotelInfo 酒店信息
     * @return 是否更新成功
     */
    boolean updateHotel(HotelInfo hotelInfo);

    /**
     * 删除酒店信息（逻辑删除）
     * 
     * @param hotelId 酒店ID
     * @return 是否删除成功
     */
    boolean deleteHotel(Long hotelId);

    /**
     * 批量删除酒店信息
     * 
     * @param hotelIds 酒店ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteHotels(List<Long> hotelIds);

    /**
     * 根据ID获取酒店信息
     * 
     * @param hotelId 酒店ID
     * @return 酒店信息
     */
    HotelInfo getHotelById(Long hotelId);

    /**
     * 获取酒店统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getHotelStatistics();

    /**
     * 检查酒店名称是否已存在
     * 
     * @param hotelName 酒店名称
     * @param excludeId 排除的酒店ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isHotelNameExists(String hotelName, Long excludeId);

    /**
     * 导入酒店数据
     * 
     * @param hotelInfoList 酒店信息列表
     * @return 导入结果
     */
    Map<String, Object> importHotels(List<HotelInfo> hotelInfoList);

    /**
     * 导出酒店数据
     * 
     * @param hotelName 酒店名称（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 酒店信息列表
     */
    List<HotelInfo> exportHotels(String hotelName, BigDecimal minPrice, BigDecimal maxPrice,
                                LocalDate startDate, LocalDate endDate);
}

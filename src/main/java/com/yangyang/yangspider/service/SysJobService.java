package com.yangyang.yangspider.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yangyang.yangspider.entity.SysJob;
import org.quartz.SchedulerException;

import java.util.List;
import java.util.Map;

/**
 * 定时任务服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
public interface SysJobService extends IService<SysJob> {

    /**
     * 分页查询定时任务
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param jobName 任务名称（可选）
     * @param jobGroup 任务组名（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    IPage<SysJob> pageQuery(Long current, Long size, String jobName, String jobGroup, Integer status);

    /**
     * 创建定时任务
     * 
     * @param sysJob 任务信息
     * @return 是否创建成功
     * @throws SchedulerException 调度器异常
     */
    boolean createJob(SysJob sysJob) throws SchedulerException;

    /**
     * 更新定时任务
     * 
     * @param sysJob 任务信息
     * @return 是否更新成功
     * @throws SchedulerException 调度器异常
     */
    boolean updateJob(SysJob sysJob) throws SchedulerException;

    /**
     * 删除定时任务
     * 
     * @param jobId 任务ID
     * @return 是否删除成功
     * @throws SchedulerException 调度器异常
     */
    boolean deleteJob(Long jobId) throws SchedulerException;

    /**
     * 启动定时任务
     * 
     * @param jobId 任务ID
     * @return 是否启动成功
     * @throws SchedulerException 调度器异常
     */
    boolean startJob(Long jobId) throws SchedulerException;

    /**
     * 暂停定时任务
     * 
     * @param jobId 任务ID
     * @return 是否暂停成功
     * @throws SchedulerException 调度器异常
     */
    boolean pauseJob(Long jobId) throws SchedulerException;

    /**
     * 恢复定时任务
     * 
     * @param jobId 任务ID
     * @return 是否恢复成功
     * @throws SchedulerException 调度器异常
     */
    boolean resumeJob(Long jobId) throws SchedulerException;

    /**
     * 立即执行定时任务
     * 
     * @param jobId 任务ID
     * @return 是否执行成功
     * @throws SchedulerException 调度器异常
     */
    boolean runJobNow(Long jobId) throws SchedulerException;

    /**
     * 获取任务详情（包含调度器状态）
     * 
     * @param jobId 任务ID
     * @return 任务详情
     * @throws SchedulerException 调度器异常
     */
    Map<String, Object> getJobDetail(Long jobId) throws SchedulerException;

    /**
     * 获取所有任务状态
     * 
     * @return 任务状态列表
     * @throws SchedulerException 调度器异常
     */
    List<Map<String, Object>> getAllJobStatus() throws SchedulerException;

    /**
     * 检查任务名称和组名是否已存在
     * 
     * @param jobName 任务名称
     * @param jobGroup 任务组名
     * @param excludeId 排除的任务ID
     * @return 是否存在
     */
    boolean isJobExists(String jobName, String jobGroup, Long excludeId);

    /**
     * 验证Cron表达式
     * 
     * @param cronExpression Cron表达式
     * @return 是否有效
     */
    boolean isValidCronExpression(String cronExpression);

    /**
     * 获取Cron表达式的下次执行时间
     * 
     * @param cronExpression Cron表达式
     * @param count 获取次数
     * @return 执行时间列表
     */
    List<String> getNextExecutionTimes(String cronExpression, int count);

    /**
     * 初始化系统任务
     * 从数据库加载任务到调度器
     * 
     * @throws SchedulerException 调度器异常
     */
    void initSystemJobs() throws SchedulerException;
}

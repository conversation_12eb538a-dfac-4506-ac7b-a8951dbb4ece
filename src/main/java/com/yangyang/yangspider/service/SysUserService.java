package com.yangyang.yangspider.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yangyang.yangspider.entity.SysUser;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser findByUsername(String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    SysUser findByEmail(String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    SysUser findByPhone(String phone);

    /**
     * 创建用户
     * 
     * @param sysUser 用户信息
     * @return 是否创建成功
     */
    boolean createUser(SysUser sysUser);

    /**
     * 更新用户
     * 
     * @param sysUser 用户信息
     * @return 是否更新成功
     */
    boolean updateUser(SysUser sysUser);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);

    /**
     * 启用/禁用用户
     * 
     * @param userId 用户ID
     * @param status 状态（0：禁用，1：启用）
     * @return 是否操作成功
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否重置成功
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeId);
}

package com.yangyang.yangspider.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yangyang.yangspider.entity.SysUser;
import com.yangyang.yangspider.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 用户详情服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("加载用户信息：{}", username);
        
        // 查询用户信息
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        queryWrapper.eq(SysUser::getStatus, 1); // 只查询启用的用户
        
        SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
        
        if (sysUser == null) {
            logger.warn("用户不存在：{}", username);
            throw new UsernameNotFoundException("用户不存在：" + username);
        }
        
        // 获取用户权限
        Collection<GrantedAuthority> authorities = getUserAuthorities(sysUser.getUserId());
        
        // 构建UserDetails对象
        return User.builder()
                .username(sysUser.getUsername())
                .password(sysUser.getPassword())
                .authorities(authorities)
                .accountExpired(false)
                .accountLocked(sysUser.getStatus() == 0)
                .credentialsExpired(false)
                .disabled(sysUser.getStatus() == 0)
                .build();
    }

    /**
     * 获取用户权限
     * 
     * @param userId 用户ID
     * @return 权限集合
     */
    private Collection<GrantedAuthority> getUserAuthorities(Long userId) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 这里简化处理，实际项目中应该从数据库查询用户角色和权限
        // 为admin用户分配管理员权限
        if (userId == 1L) {
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            authorities.add(new SimpleGrantedAuthority("hotel:create"));
            authorities.add(new SimpleGrantedAuthority("hotel:update"));
            authorities.add(new SimpleGrantedAuthority("hotel:delete"));
            authorities.add(new SimpleGrantedAuthority("hotel:query"));
            authorities.add(new SimpleGrantedAuthority("job:manage"));
        } else {
            // 普通用户只有查询权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            authorities.add(new SimpleGrantedAuthority("hotel:query"));
        }
        
        return authorities;
    }
}

package com.yangyang.yangspider.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yangyang.yangspider.entity.HotelInfo;
import com.yangyang.yangspider.mapper.HotelInfoMapper;
import com.yangyang.yangspider.service.HotelInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 酒店信息服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HotelInfoServiceImpl extends ServiceImpl<HotelInfoMapper, HotelInfo> implements HotelInfoService {

    @Override
    public IPage<HotelInfo> pageQuery(Long current, Long size, String hotelName,
                                     BigDecimal minPrice, BigDecimal maxPrice,
                                     LocalDate startDate, LocalDate endDate) {
        Page<HotelInfo> page = new Page<>(current, size);
        return baseMapper.selectHotelInfoPage(page, hotelName, minPrice, maxPrice, startDate, endDate);
    }

    @Override
    public List<HotelInfo> findByHotelNameLike(String hotelName) {
        return baseMapper.findByHotelNameLike(hotelName);
    }

    @Override
    public List<HotelInfo> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return baseMapper.findByPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<HotelInfo> findByDateRange(LocalDate startDate, LocalDate endDate) {
        return baseMapper.findByDateRange(startDate, endDate);
    }

    @Override
    public List<HotelInfo> findByLocationRange(BigDecimal minLongitude, BigDecimal maxLongitude,
                                              BigDecimal minLatitude, BigDecimal maxLatitude) {
        return baseMapper.findByLocationRange(minLongitude, maxLongitude, minLatitude, maxLatitude);
    }

    @Override
    public boolean createHotel(HotelInfo hotelInfo) {
        // 检查酒店名称是否已存在
        if (isHotelNameExists(hotelInfo.getHotelName(), null)) {
            throw new RuntimeException("酒店名称已存在：" + hotelInfo.getHotelName());
        }
        
        return this.save(hotelInfo);
    }

    @Override
    public boolean updateHotel(HotelInfo hotelInfo) {
        // 检查酒店名称是否已存在（排除当前酒店）
        if (isHotelNameExists(hotelInfo.getHotelName(), hotelInfo.getHotelId())) {
            throw new RuntimeException("酒店名称已存在：" + hotelInfo.getHotelName());
        }
        
        return this.updateById(hotelInfo);
    }

    @Override
    public boolean deleteHotel(Long hotelId) {
        return this.removeById(hotelId);
    }

    @Override
    public boolean batchDeleteHotels(List<Long> hotelIds) {
        return this.removeByIds(hotelIds);
    }

    @Override
    public HotelInfo getHotelById(Long hotelId) {
        return this.getById(hotelId);
    }

    @Override
    public Map<String, Object> getHotelStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总数统计
        Long totalCount = baseMapper.countHotels();
        statistics.put("totalCount", totalCount);
        
        // 价格统计
        List<Object> priceStats = baseMapper.getPriceStatistics();
        statistics.put("priceStatistics", priceStats);
        
        // 按日期统计（最近7天）
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(7);
        List<HotelInfo> recentHotels = findByDateRange(startDate, endDate);
        statistics.put("recentCount", recentHotels.size());
        
        return statistics;
    }

    @Override
    public boolean isHotelNameExists(String hotelName, Long excludeId) {
        LambdaQueryWrapper<HotelInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelInfo::getHotelName, hotelName);
        
        if (excludeId != null) {
            queryWrapper.ne(HotelInfo::getHotelId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public Map<String, Object> importHotels(List<HotelInfo> hotelInfoList) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        for (HotelInfo hotelInfo : hotelInfoList) {
            try {
                // 检查必填字段
                if (!StringUtils.hasText(hotelInfo.getHotelName()) ||
                    hotelInfo.getHotelLongitude() == null ||
                    hotelInfo.getHotelLatitude() == null ||
                    !StringUtils.hasText(hotelInfo.getHotelAddress()) ||
                    hotelInfo.getHotelDate() == null ||
                    hotelInfo.getHotelPrice() == null) {
                    failCount++;
                    errorMessages.append("酒店信息不完整：").append(hotelInfo.getHotelName()).append("\n");
                    continue;
                }
                
                // 检查酒店名称是否已存在
                if (isHotelNameExists(hotelInfo.getHotelName(), null)) {
                    failCount++;
                    errorMessages.append("酒店名称已存在：").append(hotelInfo.getHotelName()).append("\n");
                    continue;
                }
                
                this.save(hotelInfo);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("导入失败：").append(hotelInfo.getHotelName())
                           .append("，错误信息：").append(e.getMessage()).append("\n");
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages.toString());
        
        return result;
    }

    @Override
    public List<HotelInfo> exportHotels(String hotelName, BigDecimal minPrice, BigDecimal maxPrice,
                                       LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<HotelInfo> queryWrapper = new LambdaQueryWrapper<>();
        
        // 酒店名称模糊查询
        if (StringUtils.hasText(hotelName)) {
            queryWrapper.like(HotelInfo::getHotelName, hotelName);
        }
        
        // 价格区间查询
        if (minPrice != null) {
            queryWrapper.ge(HotelInfo::getHotelPrice, minPrice);
        }
        if (maxPrice != null) {
            queryWrapper.le(HotelInfo::getHotelPrice, maxPrice);
        }
        
        // 日期范围查询
        if (startDate != null) {
            queryWrapper.ge(HotelInfo::getHotelDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.le(HotelInfo::getHotelDate, endDate);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(HotelInfo::getCreateTime);
        
        return this.list(queryWrapper);
    }
}

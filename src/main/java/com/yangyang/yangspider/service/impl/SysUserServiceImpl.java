package com.yangyang.yangspider.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yangyang.yangspider.entity.SysUser;
import com.yangyang.yangspider.mapper.SysUserMapper;
import com.yangyang.yangspider.service.SysUserService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

/**
 * 系统用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public SysUser findByUsername(String username) {
        return baseMapper.findByUsername(username);
    }

    @Override
    public SysUser findByEmail(String email) {
        return baseMapper.findByEmail(email);
    }

    @Override
    public SysUser findByPhone(String phone) {
        return baseMapper.findByPhone(phone);
    }

    @Override
    public boolean createUser(SysUser sysUser) {
        // 检查用户名是否已存在
        if (isUsernameExists(sysUser.getUsername(), null)) {
            throw new RuntimeException("用户名已存在：" + sysUser.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(sysUser.getEmail()) && isEmailExists(sysUser.getEmail(), null)) {
            throw new RuntimeException("邮箱已存在：" + sysUser.getEmail());
        }
        
        // 检查手机号是否已存在
        if (StringUtils.hasText(sysUser.getPhone()) && isPhoneExists(sysUser.getPhone(), null)) {
            throw new RuntimeException("手机号已存在：" + sysUser.getPhone());
        }
        
        // 加密密码
        sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        
        // 设置默认状态
        if (sysUser.getStatus() == null) {
            sysUser.setStatus(1);
        }
        
        return this.save(sysUser);
    }

    @Override
    public boolean updateUser(SysUser sysUser) {
        // 检查用户名是否已存在（排除当前用户）
        if (isUsernameExists(sysUser.getUsername(), sysUser.getUserId())) {
            throw new RuntimeException("用户名已存在：" + sysUser.getUsername());
        }
        
        // 检查邮箱是否已存在（排除当前用户）
        if (StringUtils.hasText(sysUser.getEmail()) && isEmailExists(sysUser.getEmail(), sysUser.getUserId())) {
            throw new RuntimeException("邮箱已存在：" + sysUser.getEmail());
        }
        
        // 检查手机号是否已存在（排除当前用户）
        if (StringUtils.hasText(sysUser.getPhone()) && isPhoneExists(sysUser.getPhone(), sysUser.getUserId())) {
            throw new RuntimeException("手机号已存在：" + sysUser.getPhone());
        }
        
        // 如果密码不为空，则加密密码
        if (StringUtils.hasText(sysUser.getPassword())) {
            sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        }
        
        return this.updateById(sysUser);
    }

    @Override
    public boolean deleteUser(Long userId) {
        return this.removeById(userId);
    }

    @Override
    public boolean updateUserStatus(Long userId, Integer status) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setStatus(status);
        return this.updateById(sysUser);
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // 获取用户信息
        SysUser sysUser = this.getById(userId);
        if (sysUser == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, sysUser.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }
        
        // 更新密码
        sysUser.setPassword(passwordEncoder.encode(newPassword));
        return this.updateById(sysUser);
    }

    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setPassword(passwordEncoder.encode(newPassword));
        return this.updateById(sysUser);
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        
        if (excludeId != null) {
            queryWrapper.ne(SysUser::getUserId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getEmail, email);
        
        if (excludeId != null) {
            queryWrapper.ne(SysUser::getUserId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getPhone, phone);
        
        if (excludeId != null) {
            queryWrapper.ne(SysUser::getUserId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }
}

package com.yangyang.yangspider.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yangyang.yangspider.common.Result;
import com.yangyang.yangspider.common.ResultCode;
import com.yangyang.yangspider.dto.HotelQueryDTO;
import com.yangyang.yangspider.entity.HotelInfo;
import com.yangyang.yangspider.service.HotelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 酒店信息控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@RestController
@RequestMapping("/hotels")
@Tag(name = "酒店管理", description = "酒店信息的增删改查操作")
@Validated
public class HotelController {

    @Autowired
    private HotelInfoService hotelInfoService;

    /**
     * 分页查询酒店信息
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询酒店信息", description = "支持多条件分页查询酒店信息")
    public Result<IPage<HotelInfo>> pageQuery(@Valid @RequestBody HotelQueryDTO queryDTO) {
        try {
            IPage<HotelInfo> page = hotelInfoService.pageQuery(
                queryDTO.getCurrent(),
                queryDTO.getSize(),
                queryDTO.getHotelName(),
                queryDTO.getMinPrice(),
                queryDTO.getMaxPrice(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate()
            );
            return Result.success("查询成功", page);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取酒店信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取酒店信息", description = "根据酒店ID获取详细信息")
    public Result<HotelInfo> getById(@Parameter(description = "酒店ID") @PathVariable @NotNull Long id) {
        try {
            HotelInfo hotelInfo = hotelInfoService.getHotelById(id);
            if (hotelInfo == null) {
                return Result.error(ResultCode.HOTEL_NOT_FOUND);
            }
            return Result.success("查询成功", hotelInfo);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建酒店信息
     */
    @PostMapping
    @Operation(summary = "创建酒店信息", description = "新增酒店信息")
    public Result<HotelInfo> create(@Valid @RequestBody HotelInfo hotelInfo) {
        try {
            boolean success = hotelInfoService.createHotel(hotelInfo);
            if (success) {
                return Result.success("创建成功", hotelInfo);
            } else {
                return Result.error(ResultCode.HOTEL_CREATE_FAILED);
            }
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新酒店信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新酒店信息", description = "根据ID更新酒店信息")
    public Result<HotelInfo> update(@Parameter(description = "酒店ID") @PathVariable @NotNull Long id,
                                   @Valid @RequestBody HotelInfo hotelInfo) {
        try {
            // 检查酒店是否存在
            HotelInfo existingHotel = hotelInfoService.getHotelById(id);
            if (existingHotel == null) {
                return Result.error(ResultCode.HOTEL_NOT_FOUND);
            }
            
            hotelInfo.setHotelId(id);
            boolean success = hotelInfoService.updateHotel(hotelInfo);
            if (success) {
                return Result.success("更新成功", hotelInfo);
            } else {
                return Result.error(ResultCode.HOTEL_UPDATE_FAILED);
            }
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除酒店信息
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除酒店信息", description = "根据ID删除酒店信息（逻辑删除）")
    public Result<Void> delete(@Parameter(description = "酒店ID") @PathVariable @NotNull Long id) {
        try {
            // 检查酒店是否存在
            HotelInfo existingHotel = hotelInfoService.getHotelById(id);
            if (existingHotel == null) {
                return Result.error(ResultCode.HOTEL_NOT_FOUND);
            }
            
            boolean success = hotelInfoService.deleteHotel(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error(ResultCode.HOTEL_DELETE_FAILED);
            }
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除酒店信息
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除酒店信息", description = "根据ID列表批量删除酒店信息")
    public Result<Void> batchDelete(@Parameter(description = "酒店ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean success = hotelInfoService.batchDeleteHotels(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.error(ResultCode.HOTEL_DELETE_FAILED);
            }
        } catch (Exception e) {
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据酒店名称模糊查询
     */
    @GetMapping("/search/name")
    @Operation(summary = "根据名称搜索酒店", description = "根据酒店名称进行模糊查询")
    public Result<List<HotelInfo>> searchByName(@Parameter(description = "酒店名称") @RequestParam String hotelName) {
        try {
            List<HotelInfo> hotels = hotelInfoService.findByHotelNameLike(hotelName);
            return Result.success("查询成功", hotels);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据价格区间查询
     */
    @GetMapping("/search/price")
    @Operation(summary = "根据价格区间查询酒店", description = "根据价格范围查询酒店信息")
    public Result<List<HotelInfo>> searchByPriceRange(@Parameter(description = "最低价格") @RequestParam java.math.BigDecimal minPrice,
                                                      @Parameter(description = "最高价格") @RequestParam java.math.BigDecimal maxPrice) {
        try {
            List<HotelInfo> hotels = hotelInfoService.findByPriceRange(minPrice, maxPrice);
            return Result.success("查询成功", hotels);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取酒店统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取酒店统计信息", description = "获取酒店数量、价格等统计信息")
    public Result<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = hotelInfoService.getHotelStatistics();
            return Result.success("查询成功", statistics);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导入酒店数据
     */
    @PostMapping("/import")
    @Operation(summary = "导入酒店数据", description = "批量导入酒店信息")
    public Result<Map<String, Object>> importHotels(@Valid @RequestBody List<HotelInfo> hotelInfoList) {
        try {
            Map<String, Object> result = hotelInfoService.importHotels(hotelInfoList);
            return Result.success("导入完成", result);
        } catch (Exception e) {
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出酒店数据
     */
    @PostMapping("/export")
    @Operation(summary = "导出酒店数据", description = "根据条件导出酒店信息")
    public Result<List<HotelInfo>> exportHotels(@RequestBody HotelQueryDTO queryDTO) {
        try {
            List<HotelInfo> hotels = hotelInfoService.exportHotels(
                queryDTO.getHotelName(),
                queryDTO.getMinPrice(),
                queryDTO.getMaxPrice(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate()
            );
            return Result.success("导出成功", hotels);
        } catch (Exception e) {
            return Result.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 检查酒店名称是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查酒店名称", description = "检查酒店名称是否已存在")
    public Result<Boolean> checkHotelName(@Parameter(description = "酒店名称") @RequestParam String hotelName,
                                         @Parameter(description = "排除的酒店ID") @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = hotelInfoService.isHotelNameExists(hotelName, excludeId);
            return Result.success("检查完成", exists);
        } catch (Exception e) {
            return Result.error("检查失败：" + e.getMessage());
        }
    }
}

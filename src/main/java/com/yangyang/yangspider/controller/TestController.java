package com.yangyang.yangspider.controller;

import com.yangyang.yangspider.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@RestController
@RequestMapping("/test")
@Tag(name = "系统测试", description = "系统功能测试接口")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查系统是否正常运行")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("application", "YangSpider Hotel Management System");
        data.put("version", "1.0.0");
        
        return Result.success("系统运行正常", data);
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    @Operation(summary = "系统信息", description = "获取系统基本信息")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("applicationName", "YangSpider酒店管理系统");
        data.put("version", "1.0.0");
        data.put("author", "YangYang");
        data.put("description", "基于Spring Boot的酒店信息管理系统");
        data.put("javaVersion", System.getProperty("java.version"));
        data.put("springBootVersion", "3.2.0");
        data.put("buildTime", LocalDateTime.now());
        
        return Result.success("获取成功", data);
    }
}

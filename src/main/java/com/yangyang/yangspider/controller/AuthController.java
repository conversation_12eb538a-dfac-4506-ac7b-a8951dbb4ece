package com.yangyang.yangspider.controller;

import com.yangyang.yangspider.common.JwtUtils;
import com.yangyang.yangspider.common.Result;
import com.yangyang.yangspider.common.ResultCode;
import com.yangyang.yangspider.dto.LoginDTO;
import com.yangyang.yangspider.dto.LoginResponseDTO;
import com.yangyang.yangspider.entity.SysUser;
import com.yangyang.yangspider.service.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "用户认证", description = "用户登录、登出、Token刷新等认证相关操作")
@Validated
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private SysUserService sysUserService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT Token")
    public Result<LoginResponseDTO> login(@Valid @RequestBody LoginDTO loginDTO) {
        try {
            logger.info("用户登录尝试：{}", loginDTO.getUsername());
            
            // 创建认证Token
            UsernamePasswordAuthenticationToken authToken = 
                new UsernamePasswordAuthenticationToken(loginDTO.getUsername(), loginDTO.getPassword());
            
            // 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);
            
            // 认证成功，获取用户信息
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            SysUser sysUser = sysUserService.findByUsername(userDetails.getUsername());
            
            // 生成JWT Token
            String token = jwtUtils.generateToken(sysUser.getUsername(), sysUser.getUserId());
            
            // 构建响应
            LoginResponseDTO responseDTO = new LoginResponseDTO();
            responseDTO.setToken(token);
            responseDTO.setTokenType("Bearer");
            responseDTO.setExpiresIn(jwtUtils.getExpiration());
            responseDTO.setUserId(sysUser.getUserId());
            responseDTO.setUsername(sysUser.getUsername());
            responseDTO.setRealName(sysUser.getRealName());
            responseDTO.setEmail(sysUser.getEmail());
            
            logger.info("用户登录成功：{}", loginDTO.getUsername());
            return Result.success("登录成功", responseDTO);
            
        } catch (AuthenticationException e) {
            logger.warn("用户登录失败：{}，原因：{}", loginDTO.getUsername(), e.getMessage());
            return Result.error(ResultCode.USER_PASSWORD_ERROR);
        } catch (Exception e) {
            logger.error("用户登录异常：{}，原因：", loginDTO.getUsername(), e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，清除认证信息")
    public Result<Void> logout(HttpServletRequest request) {
        try {
            // 获取当前认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                String username = authentication.getName();
                logger.info("用户登出：{}", username);
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
            return Result.success("登出成功");
        } catch (Exception e) {
            logger.error("用户登出异常：", e);
            return Result.error("登出失败：" + e.getMessage());
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新Token", description = "使用当前Token刷新获取新的Token")
    public Result<LoginResponseDTO> refreshToken(HttpServletRequest request) {
        try {
            // 从请求头获取Token
            String authHeader = request.getHeader(jwtUtils.getHeader());
            String token = jwtUtils.getTokenFromHeader(authHeader);
            
            if (token == null) {
                return Result.error(ResultCode.TOKEN_INVALID);
            }
            
            // 刷新Token
            String newToken = jwtUtils.refreshToken(token);
            if (newToken == null) {
                return Result.error(ResultCode.TOKEN_EXPIRED);
            }
            
            // 获取用户信息
            String username = jwtUtils.getUsernameFromToken(newToken);
            Long userId = jwtUtils.getUserIdFromToken(newToken);
            SysUser sysUser = sysUserService.findByUsername(username);
            
            // 构建响应
            LoginResponseDTO responseDTO = new LoginResponseDTO();
            responseDTO.setToken(newToken);
            responseDTO.setTokenType("Bearer");
            responseDTO.setExpiresIn(jwtUtils.getExpiration());
            responseDTO.setUserId(userId);
            responseDTO.setUsername(username);
            responseDTO.setRealName(sysUser.getRealName());
            responseDTO.setEmail(sysUser.getEmail());
            
            logger.info("Token刷新成功：{}", username);
            return Result.success("Token刷新成功", responseDTO);
            
        } catch (Exception e) {
            logger.error("Token刷新异常：", e);
            return Result.error("Token刷新失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public Result<SysUser> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return Result.error(ResultCode.UNAUTHORIZED);
            }
            
            String username = authentication.getName();
            SysUser sysUser = sysUserService.findByUsername(username);
            
            if (sysUser == null) {
                return Result.error(ResultCode.USER_NOT_FOUND);
            }
            
            return Result.success("获取成功", sysUser);
        } catch (Exception e) {
            logger.error("获取当前用户信息异常：", e);
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }
}

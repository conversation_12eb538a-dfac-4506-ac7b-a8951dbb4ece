package com.yangyang.yangspider;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * YangSpider酒店管理系统启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@SpringBootApplication
@MapperScan("com.yangyang.yangspider.mapper")
@EnableScheduling
public class YangSpiderApplication {

    public static void main(String[] args) {
        SpringApplication.run(YangSpiderApplication.class, args);
        System.out.println("=================================");
        System.out.println("YangSpider酒店管理系统启动成功！");
        System.out.println("Swagger文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("=================================");
    }
}

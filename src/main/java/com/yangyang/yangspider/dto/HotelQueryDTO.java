package com.yangyang.yangspider.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 酒店查询DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Schema(description = "酒店查询参数")
public class HotelQueryDTO {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    @Schema(description = "当前页码", example = "1")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Schema(description = "每页大小", example = "10")
    private Long size = 10L;

    /**
     * 酒店名称（模糊查询）
     */
    @Size(max = 200, message = "酒店名称长度不能超过200个字符")
    @Schema(description = "酒店名称", example = "希尔顿")
    private String hotelName;

    /**
     * 最低价格
     */
    @DecimalMin(value = "0.0", message = "最低价格不能为负数")
    @Schema(description = "最低价格", example = "100.00")
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    @DecimalMin(value = "0.0", message = "最高价格不能为负数")
    @Schema(description = "最高价格", example = "1000.00")
    private BigDecimal maxPrice;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期", example = "2024-08-01")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期", example = "2024-08-31")
    private LocalDate endDate;

    /**
     * 最小经度
     */
    @Schema(description = "最小经度", example = "116.0")
    private BigDecimal minLongitude;

    /**
     * 最大经度
     */
    @Schema(description = "最大经度", example = "117.0")
    private BigDecimal maxLongitude;

    /**
     * 最小纬度
     */
    @Schema(description = "最小纬度", example = "39.0")
    private BigDecimal minLatitude;

    /**
     * 最大纬度
     */
    @Schema(description = "最大纬度", example = "40.0")
    private BigDecimal maxLatitude;

    /**
     * 地址关键词
     */
    @Size(max = 500, message = "地址关键词长度不能超过500个字符")
    @Schema(description = "地址关键词", example = "北京")
    private String addressKeyword;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime", allowableValues = {"createTime", "hotelPrice", "hotelDate", "hotelName"})
    private String sortField = "createTime";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";

    // 构造函数
    public HotelQueryDTO() {}

    // Getter和Setter方法
    public Long getCurrent() {
        return current;
    }

    public void setCurrent(Long current) {
        this.current = current;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getMinLongitude() {
        return minLongitude;
    }

    public void setMinLongitude(BigDecimal minLongitude) {
        this.minLongitude = minLongitude;
    }

    public BigDecimal getMaxLongitude() {
        return maxLongitude;
    }

    public void setMaxLongitude(BigDecimal maxLongitude) {
        this.maxLongitude = maxLongitude;
    }

    public BigDecimal getMinLatitude() {
        return minLatitude;
    }

    public void setMinLatitude(BigDecimal minLatitude) {
        this.minLatitude = minLatitude;
    }

    public BigDecimal getMaxLatitude() {
        return maxLatitude;
    }

    public void setMaxLatitude(BigDecimal maxLatitude) {
        this.maxLatitude = maxLatitude;
    }

    public String getAddressKeyword() {
        return addressKeyword;
    }

    public void setAddressKeyword(String addressKeyword) {
        this.addressKeyword = addressKeyword;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "HotelQueryDTO{" +
                "current=" + current +
                ", size=" + size +
                ", hotelName='" + hotelName + '\'' +
                ", minPrice=" + minPrice +
                ", maxPrice=" + maxPrice +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", minLongitude=" + minLongitude +
                ", maxLongitude=" + maxLongitude +
                ", minLatitude=" + minLatitude +
                ", maxLatitude=" + maxLatitude +
                ", addressKeyword='" + addressKeyword + '\'' +
                ", sortField='" + sortField + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                '}';
    }
}

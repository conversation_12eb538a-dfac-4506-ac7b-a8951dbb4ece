package com.yangyang.yangspider.common;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),

    // 服务器错误
    ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误
    HOTEL_NOT_FOUND(1001, "酒店信息不存在"),
    HOTEL_NAME_EXISTS(1002, "酒店名称已存在"),
    HOTEL_DELETE_FAILED(1003, "酒店删除失败"),
    HOTEL_UPDATE_FAILED(1004, "酒店更新失败"),
    HOTEL_CREATE_FAILED(1005, "酒店创建失败"),

    // 用户相关错误
    USER_NOT_FOUND(2001, "用户不存在"),
    USER_PASSWORD_ERROR(2002, "用户密码错误"),
    USER_DISABLED(2003, "用户已被禁用"),
    TOKEN_INVALID(2004, "Token无效"),
    TOKEN_EXPIRED(2005, "Token已过期"),

    // 权限相关错误
    PERMISSION_DENIED(3001, "权限不足"),
    ROLE_NOT_FOUND(3002, "角色不存在"),

    // 任务相关错误
    JOB_NOT_FOUND(4001, "定时任务不存在"),
    JOB_ALREADY_EXISTS(4002, "定时任务已存在"),
    JOB_EXECUTE_FAILED(4003, "定时任务执行失败"),
    JOB_PAUSE_FAILED(4004, "定时任务暂停失败"),
    JOB_RESUME_FAILED(4005, "定时任务恢复失败"),
    JOB_DELETE_FAILED(4006, "定时任务删除失败"),

    // 文件相关错误
    FILE_UPLOAD_FAILED(5001, "文件上传失败"),
    FILE_NOT_FOUND(5002, "文件不存在"),
    FILE_TYPE_ERROR(5003, "文件类型错误"),
    FILE_SIZE_EXCEEDED(5004, "文件大小超出限制"),

    // 数据库相关错误
    DATABASE_ERROR(6001, "数据库操作失败"),
    DATA_INTEGRITY_ERROR(6002, "数据完整性错误"),
    DUPLICATE_KEY_ERROR(6003, "数据重复"),

    // 外部服务错误
    EXTERNAL_SERVICE_ERROR(7001, "外部服务调用失败"),
    NETWORK_ERROR(7002, "网络连接错误"),
    TIMEOUT_ERROR(7003, "请求超时");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "ResultCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}

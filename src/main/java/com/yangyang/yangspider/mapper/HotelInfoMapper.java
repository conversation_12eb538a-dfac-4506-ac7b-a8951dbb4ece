package com.yangyang.yangspider.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yangyang.yangspider.entity.HotelInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 酒店信息Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Mapper
public interface HotelInfoMapper extends BaseMapper<HotelInfo> {

    /**
     * 根据酒店名称模糊查询酒店信息
     * 
     * @param hotelName 酒店名称
     * @return 酒店信息列表
     */
    @Select("SELECT * FROM hotel_info WHERE deleted = 0 AND hotel_name LIKE CONCAT('%', #{hotelName}, '%') ORDER BY create_time DESC")
    List<HotelInfo> findByHotelNameLike(@Param("hotelName") String hotelName);

    /**
     * 根据价格区间查询酒店信息
     * 
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 酒店信息列表
     */
    @Select("SELECT * FROM hotel_info WHERE deleted = 0 AND hotel_price BETWEEN #{minPrice} AND #{maxPrice} ORDER BY hotel_price ASC")
    List<HotelInfo> findByPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice);

    /**
     * 根据日期范围查询酒店信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 酒店信息列表
     */
    @Select("SELECT * FROM hotel_info WHERE deleted = 0 AND hotel_date BETWEEN #{startDate} AND #{endDate} ORDER BY hotel_date DESC")
    List<HotelInfo> findByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据地理位置范围查询酒店信息
     * 
     * @param minLongitude 最小经度
     * @param maxLongitude 最大经度
     * @param minLatitude 最小纬度
     * @param maxLatitude 最大纬度
     * @return 酒店信息列表
     */
    @Select("SELECT * FROM hotel_info WHERE deleted = 0 " +
            "AND hotel_longitude BETWEEN #{minLongitude} AND #{maxLongitude} " +
            "AND hotel_latitude BETWEEN #{minLatitude} AND #{maxLatitude} " +
            "ORDER BY create_time DESC")
    List<HotelInfo> findByLocationRange(@Param("minLongitude") BigDecimal minLongitude,
                                       @Param("maxLongitude") BigDecimal maxLongitude,
                                       @Param("minLatitude") BigDecimal minLatitude,
                                       @Param("maxLatitude") BigDecimal maxLatitude);

    /**
     * 分页查询酒店信息（支持多条件查询）
     * 
     * @param page 分页参数
     * @param hotelName 酒店名称（可选）
     * @param minPrice 最低价格（可选）
     * @param maxPrice 最高价格（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 分页结果
     */
    IPage<HotelInfo> selectHotelInfoPage(Page<HotelInfo> page,
                                        @Param("hotelName") String hotelName,
                                        @Param("minPrice") BigDecimal minPrice,
                                        @Param("maxPrice") BigDecimal maxPrice,
                                        @Param("startDate") LocalDate startDate,
                                        @Param("endDate") LocalDate endDate);

    /**
     * 统计酒店总数
     * 
     * @return 酒店总数
     */
    @Select("SELECT COUNT(*) FROM hotel_info WHERE deleted = 0")
    Long countHotels();

    /**
     * 获取价格统计信息
     * 
     * @return 价格统计信息（最低价、最高价、平均价）
     */
    @Select("SELECT MIN(hotel_price) as minPrice, MAX(hotel_price) as maxPrice, AVG(hotel_price) as avgPrice FROM hotel_info WHERE deleted = 0")
    List<Object> getPriceStatistics();
}

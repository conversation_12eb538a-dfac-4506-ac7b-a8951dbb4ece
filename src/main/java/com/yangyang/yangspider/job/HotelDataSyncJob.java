package com.yangyang.yangspider.job;

import com.yangyang.yangspider.service.HotelInfoService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 酒店数据同步定时任务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-08
 */
@Component
public class HotelDataSyncJob implements Job {

    private static final Logger logger = LoggerFactory.getLogger(HotelDataSyncJob.class);

    @Autowired
    private HotelInfoService hotelInfoService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        String jobName = context.getJobDetail().getKey().getName();
        String jobGroup = context.getJobDetail().getKey().getGroup();
        
        logger.info("开始执行定时任务：{}.{}", jobGroup, jobName);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行酒店数据同步逻辑
            syncHotelData();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            logger.info("定时任务执行成功：{}.{}，耗时：{}ms", jobGroup, jobName, duration);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            logger.error("定时任务执行失败：{}.{}，耗时：{}ms，错误信息：", jobGroup, jobName, duration, e);
            throw new JobExecutionException("酒店数据同步任务执行失败", e);
        }
    }

    /**
     * 同步酒店数据
     */
    private void syncHotelData() {
        logger.info("开始同步酒店数据...");
        
        try {
            // 这里可以实现具体的数据同步逻辑
            // 例如：从外部API获取数据、清理过期数据、更新统计信息等
            
            // 模拟数据同步过程
            Thread.sleep(2000); // 模拟耗时操作
            
            // 获取统计信息
            var statistics = hotelInfoService.getHotelStatistics();
            logger.info("当前酒店数据统计：{}", statistics);
            
            // 记录同步时间
            String syncTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            logger.info("酒店数据同步完成，同步时间：{}", syncTime);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("数据同步被中断", e);
        } catch (Exception e) {
            throw new RuntimeException("数据同步过程中发生错误", e);
        }
    }
}

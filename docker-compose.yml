version: '3.8'

services:
  # YangSpider应用服务
  yangspider-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yangspider-app
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - JAVA_OPTS=-Xms512m -Xmx1024m
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - yangspider-mysql
    networks:
      - yangspider-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/test/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL数据库服务（可选，如果使用外部数据库可以注释掉）
  yangspider-mysql:
    image: mysql:8.0
    container_name: yangspider-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: yangyangchuyou
      MYSQL_USER: yangyangchuyou
      MYSQL_PASSWORD: Yangyang123@qwe
      MYSQL_ROOT_HOST: '%'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - yangspider-network
    restart: unless-stopped

  # Redis缓存服务（可选）
  yangspider-redis:
    image: redis:7-alpine
    container_name: yangspider-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - yangspider-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理（可选）
  yangspider-nginx:
    image: nginx:alpine
    container_name: yangspider-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - yangspider-app
    networks:
      - yangspider-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  yangspider-network:
    driver: bridge

@echo off
echo ========================================
echo YangSpider酒店管理系统启动脚本
echo ========================================

echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请安装JDK 17或更高版本
    pause
    exit /b 1
)

echo 正在检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请安装Maven 3.9或更高版本
    pause
    exit /b 1
)

echo 正在编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo 正在启动应用...
echo 应用将在 http://localhost:8080/api 启动
echo Swagger文档地址: http://localhost:8080/api/swagger-ui.html
echo 按 Ctrl+C 停止应用

mvn spring-boot:run

pause

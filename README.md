# YangSpider 酒店管理系统

基于Spring Boot的现代化酒店信息管理系统，提供完整的酒店信息CRUD操作、用户认证、定时任务管理等功能。

## 🚀 项目特性

- **现代化技术栈**: 基于Spring Boot 3.2.0 + Java 17
- **完整的CRUD操作**: 酒店信息的增删改查功能
- **用户认证授权**: 基于Spring Security + JWT的安全认证
- **API文档**: 集成Swagger/OpenAPI 3自动生成API文档
- **定时任务**: 基于Quartz的定时任务调度系统
- **数据持久化**: MyBatis Plus + MySQL数据库
- **前端管理**: Vue.js后台管理界面
- **容器化部署**: 支持Docker部署

## 🛠 技术栈

### 后端技术
- **Java**: 17
- **框架**: Spring Boot 3.2.0
- **安全**: Spring Security 6.2.0
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.4.1
- **任务调度**: Quartz 2.3.2
- **API文档**: Swagger/OpenAPI 3
- **构建工具**: Maven 3.9+

### 前端技术
- **框架**: Vue.js 3
- **UI组件**: Element Plus
- **HTTP客户端**: Axios
- **路由**: Vue Router
- **状态管理**: Pinia

### 数据库配置
- **服务器**: rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com:3306
- **数据库**: yangyangchuyou
- **用户名**: yangyangchuyou
- **密码**: Yangyang123@qwe

## 📋 功能模块

### 1. 酒店信息管理
- 酒店信息的增加、删除、修改、查询
- 支持分页查询和多条件搜索
- 按价格区间、日期范围、地理位置查询
- 数据导入导出功能

### 2. 用户认证系统
- JWT Token认证
- 用户登录/登出
- 权限控制和访问保护
- 默认管理员账户：admin/1234@qwer

### 3. 定时任务管理
- 基于Quartz的任务调度
- 任务的启动、暂停、恢复、删除
- 任务执行状态监控
- 支持Cron表达式配置

### 4. API文档
- Swagger UI界面
- 完整的API接口文档
- 在线API测试功能

## 🗄 数据模型

### 酒店信息表 (hotel_info)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| hotel_id | BIGINT | 酒店ID（主键，自增） |
| hotel_name | VARCHAR(200) | 酒店名称 |
| hotel_longitude | DECIMAL(10,6) | 酒店经度 |
| hotel_latitude | DECIMAL(10,6) | 酒店纬度 |
| hotel_address | VARCHAR(500) | 酒店地址 |
| hotel_date | DATE | 酒店日期 |
| hotel_price | DECIMAL(10,2) | 酒店价格 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |
| deleted | TINYINT | 逻辑删除标志 |

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.9+
- MySQL 8.0+
- Node.js 16+ (前端开发)

### 1. 克隆项目
```bash
git clone https://github.com/yangyang/YangSpider.git
cd YangSpider
```

### 2. 数据库初始化
执行SQL脚本初始化数据库：
```bash
# 创建数据库表结构
mysql -h rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com -u yangyangchuyou -p yangyangchuyou < src/main/resources/sql/schema.sql

# 插入初始数据
mysql -h rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com -u yangyangchuyou -p yangyangchuyou < src/main/resources/sql/data.sql
```

### 3. 后端启动
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

### 4. 访问应用
- **后端API**: http://localhost:8080/api
- **Swagger文档**: http://localhost:8080/api/swagger-ui.html
- **健康检查**: http://localhost:8080/api/test/health

## 📚 API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新Token
- `GET /api/auth/me` - 获取当前用户信息

### 酒店管理接口
- `POST /api/hotels/page` - 分页查询酒店
- `GET /api/hotels/{id}` - 根据ID查询酒店
- `POST /api/hotels` - 创建酒店
- `PUT /api/hotels/{id}` - 更新酒店
- `DELETE /api/hotels/{id}` - 删除酒店
- `DELETE /api/hotels/batch` - 批量删除酒店
- `GET /api/hotels/search/name` - 按名称搜索
- `GET /api/hotels/search/price` - 按价格区间搜索
- `GET /api/hotels/statistics` - 获取统计信息

### 系统接口
- `GET /api/test/health` - 健康检查
- `GET /api/test/info` - 系统信息

## 🐳 Docker部署

### 1. 构建镜像
```bash
# 构建应用
mvn clean package -DskipTests

# 构建Docker镜像
docker build -t yangspider:latest .
```

### 2. 运行容器
```bash
# 使用docker-compose启动
docker-compose up -d
```

## 🔧 配置说明

### 应用配置 (application.yml)
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: ******************************************************************************
    username: yangyangchuyou
    password: Yangyang123@qwe

yangspider:
  jwt:
    secret: yangspider-hotel-management-system-jwt-secret-key-2024
    expiration: 86400000
```

## 👥 默认账户

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 1234@qwer | 管理员 | 拥有所有权限 |
| user | 123456 | 普通用户 | 只有查询权限 |

## 📝 开发指南

### 项目结构
```
src/main/java/com/yangyang/yangspider/
├── YangSpiderApplication.java          # 启动类
├── controller/                         # 控制器层
├── service/                           # 服务层
├── mapper/                            # 数据访问层
├── entity/                            # 实体类
├── dto/                               # 数据传输对象
├── config/                            # 配置类
├── common/                            # 通用工具类
└── job/                               # 定时任务
```

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 所有公共方法必须添加注释
- 异常处理要完整和规范

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者

**YangYang** - *项目创建者* - [GitHub](https://github.com/yangyang)

## 🙏 致谢

- Spring Boot团队提供的优秀框架
- MyBatis Plus提供的便捷ORM工具
- Swagger提供的API文档解决方案
- 所有开源项目的贡献者们

---

如有问题或建议，请提交Issue或联系作者。
